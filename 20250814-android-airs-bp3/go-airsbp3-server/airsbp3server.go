package airsbp3server

import (
	"context"
	"errors"
	"fmt"
	"log"
	"path/filepath"
	"strconv"

	airsbp3 "github.com/untillpro/airs-bp3/airsbp3"
)

var (
	abp       *airsbp3.AirsBP
	ctx       context.Context
	cancelCtx context.CancelFunc
)

// Start starts the airsbp3 server with the specified port and storage type.
// If the storage type is not recognized, it defaults to memory storage.
// Parameters:
// - port: the port on which the server will listen
// - storageType: the type of storage to use (memory or bbolt)
// - appDir: the directory where the application data is stored
func Start(port int, storageType, appDir string) {
	// Validate storageType
	switch storageType {
	case storageTypeMemory, storageTypeBBolt:
	default:
		if storageType != "" {
			log.Printf("unknown storageType '%s', using memory storage by default", storageType)

			storageType = storageTypeMemory
		}
	}

	if abp != nil {
		log.Println("airs-bp3 server already started")
		return
	}

	params := []string{
		"--vvm-port",
		strconv.Itoa(port),
		"--chargebee-site-name",
		"untillair-test",
		"--chargebee-key-secret",
		"chargebee_key",
		"--chargebee-aircluster",
		"alpha",
		"--v",
	}
	// Add BBolt storage parameters if storageType is BBolt
	if storageType == storageTypeBBolt {
		params = append(params, "--use-bbolt", "--bbolt-db-dir", filepath.Join(appDir, "./bbolt"))
	}

	airsbp := airsbp3.NewAirsBP(params)
	abp = &airsbp
	abp.SecretsReader = &mockSecretsReader{}
	abp.MetricsServicePort = 0

	ctx, cancelCtx = context.WithCancel(context.Background())
	if err := startAirsBP3(ctx, abp); err != nil {
		abp = nil
		cancelCtx()

		return
	}

	log.Println("airs-bp3 server started")
}

// Stop stops the airsbp3 server if it is running.
func Stop() {
	if abp == nil {
		log.Println("airs-bp3 server is not running")

		return
	}

	err := airsbp3.ShutdownVVM(ctx, abp)
	if err != nil {
		log.Printf("airs-bp3 server shutdown error: %v", err)
	}
	cancelCtx()

	abp = nil
	log.Println("airs-bp3 server stopped")
}

// startAirsBP3 starts the airsbp3 server and runs the necessary pipeline functions.
func startAirsBP3(ctx context.Context, abp *airsbp3.AirsBP) error {
	if abp == nil {
		err := errors.New("airs-bp3 server not started, call Start() first")
		log.Println(err.Error())

		return err
	}

	if err := runPipelineFunc(ctx, abp, airsbp3.ParseArgs); err != nil {
		return err
	}

	if err := runPipelineFunc(ctx, abp, airsbp3.DeclareAirsBP); err != nil {
		return err
	}

	if err := runPipelineFunc(ctx, abp, airsbp3.BuildVVM); err != nil {
		return err
	}

	if err := runPipelineFunc(ctx, abp, airsbp3.LaunchVVM); err != nil {
		return err
	}

	return nil
}

// runPipelineFunc runs a pipeline function with the provided context and airsbp3 instance.
func runPipelineFunc(ctx context.Context, abp *airsbp3.AirsBP, pf pipelineFunc) error {
	if err := pf(ctx, abp); err != nil {
		err := fmt.Errorf("airs-bp3 server error: %w", err)
		log.Println(err.Error())

		return err
	}

	return nil
}

// Println logs a message to the standard logger.
func Println(s string) {
	log.Println(s)
}
